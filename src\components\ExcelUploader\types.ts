// types.ts
/** excel列校验配置 */
export interface ColumnConfig {
  key: string;
  title: string;
  required?: boolean;
  type?: "string" | "number" | "date" | "enum";
  options?: string[];
  validator?: string;
  transform?: (value: any) => any;
}

/** 校验结果 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  isEmpty?: boolean;
}

/** 解析结果 */
export interface ParseResult {
  headers: string[];
  data: any[];
}

/** 校验函数 */
export interface ValidatorFunction {
  (value: any, config: ColumnConfig): ValidationResult;
}

/** excel上传器属性 */
export interface ExcelUploaderProps {
  downLoadTemplate: any;
  columnConfig: ColumnConfig[];
  onSuccess?: (data: any[]) => void;
  onError?: (error: Error) => void;
  customValidators?: Record<string, ValidatorFunction>;
  // 新增弹窗相关属性
  visible: boolean;
  onCancel: () => void;
  title?: string;
  description?: React.ReactNode;
  postProcessor?: (validData: any[]) => Promise<PostProcessResult>;
  uploadToS3?: boolean;
  useDragUploader?: boolean;
  showStepGuidance?: boolean;
  downloadIconText?: string;
}

/** 导入结果 */
export interface ImportResult {
  totalCount: number; // 总数据条数
  successCount: number; // 成功条数
  errorCount: number; // 失败条数
}

export interface ImportErrorModalProps {
  visible: boolean;
  onClose: () => void;
  importResult: ImportResult;
  onDownload: () => void;
}

export interface PostProcessResult<T = any> {
  validData: T[]; // 最终有效数据
  invalidData: T[]; // 不符合业务规则的数据
  errors?: string[]; // 错误信息
}
