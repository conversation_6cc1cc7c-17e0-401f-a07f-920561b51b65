import React, { useState } from 'react';
import { Button, Space } from 'antd';
import ExcelUploader from './ExcelUploader';
import { ColumnConfig } from './types';

const ExcelUploaderExample: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [s3Visible, setS3Visible] = useState(false);
  const [dragVisible, setDragVisible] = useState(false);
  const [s3DragVisible, setS3DragVisible] = useState(false);

  // Excel 列配置示例
  const columnConfig: ColumnConfig[] = [
    {
      key: 'name',
      title: '姓名',
      required: true,
      type: 'string'
    },
    {
      key: 'email',
      title: '邮箱',
      required: true,
      type: 'string',
      validator: 'email'
    },
    {
      key: 'age',
      title: '年龄',
      required: false,
      type: 'number'
    },
    {
      key: 'department',
      title: '部门',
      required: true,
      type: 'enum',
      options: ['技术部', '市场部', '人事部', '财务部']
    }
  ];

  // 模拟下载模板
  const handleDownloadTemplate = () => {
    console.log('下载Excel模板');
    // 这里应该实现实际的模板下载逻辑
  };

  // 本地解析成功回调
  const handleLocalSuccess = (data: any[]) => {
    console.log('本地解析成功:', data);
    // 这里可以将解析后的数据发送到服务器
  };

  // S3 上传成功回调
  const handleS3Success = (fileKey: string) => {
    console.log('S3上传成功，文件Key:', fileKey);
    // 这里可以将 fileKey 发送到服务器，让服务器处理 Excel 文件
  };

  // 错误处理
  const handleError = (error: Error) => {
    console.error('处理失败:', error);
  };

  // S3 上传错误处理
  const handleS3Error = (error: Error, body?: Object) => {
    console.error('S3上传失败:', error, body);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>ExcelUploader 组件示例</h2>
      
      <Space direction="vertical" size="middle">
        <div>
          <h3>1. 本地解析模式 - 按钮上传</h3>
          <Button type="primary" onClick={() => setVisible(true)}>
            打开本地解析上传器（按钮模式）
          </Button>
        </div>

        <div>
          <h3>2. 本地解析模式 - 拖拽上传</h3>
          <Button type="primary" onClick={() => setDragVisible(true)}>
            打开本地解析上传器（拖拽模式）
          </Button>
        </div>

        <div>
          <h3>3. S3 上传模式 - 按钮上传</h3>
          <Button type="primary" onClick={() => setS3Visible(true)}>
            打开S3上传器（按钮模式）
          </Button>
        </div>

        <div>
          <h3>4. S3 上传模式 - 拖拽上传</h3>
          <Button type="primary" onClick={() => setS3DragVisible(true)}>
            打开S3上传器（拖拽模式）
          </Button>
        </div>
      </Space>

      {/* 本地解析 - 按钮模式 */}
      <ExcelUploader
        visible={visible}
        onCancel={() => setVisible(false)}
        downLoadTemplate={handleDownloadTemplate}
        columnConfig={columnConfig}
        onSuccess={handleLocalSuccess}
        onError={handleError}
        title="本地解析上传"
        uploadToS3={false}
        useDragUploader={false}
      />

      {/* 本地解析 - 拖拽模式 */}
      <ExcelUploader
        visible={dragVisible}
        onCancel={() => setDragVisible(false)}
        downLoadTemplate={handleDownloadTemplate}
        columnConfig={columnConfig}
        onSuccess={handleLocalSuccess}
        onError={handleError}
        title="本地解析上传（拖拽）"
        uploadToS3={false}
        useDragUploader={true}
      />

      {/* S3 上传 - 按钮模式 */}
      <ExcelUploader
        visible={s3Visible}
        onCancel={() => setS3Visible(false)}
        downLoadTemplate={handleDownloadTemplate}
        columnConfig={columnConfig}
        onSuccess={handleLocalSuccess}
        onError={handleError}
        title="S3上传"
        uploadToS3={true}
        useDragUploader={false}
        s3Config={{
          getPreSignatureUrl: '/api/s3/presigned-url',
          LOPDN: 'your-domain',
          bucketName: 'your-bucket',
          maxFileSize: 50,
          unit: 'MB',
          needMd5Validete: true,
          onS3Success: handleS3Success,
          onS3Error: handleS3Error,
        }}
      />

      {/* S3 上传 - 拖拽模式 */}
      <ExcelUploader
        visible={s3DragVisible}
        onCancel={() => setS3DragVisible(false)}
        downLoadTemplate={handleDownloadTemplate}
        columnConfig={columnConfig}
        onSuccess={handleLocalSuccess}
        onError={handleError}
        title="S3上传（拖拽）"
        uploadToS3={true}
        useDragUploader={true}
        s3Config={{
          getPreSignatureUrl: '/api/s3/presigned-url',
          LOPDN: 'your-domain',
          bucketName: 'your-bucket',
          maxFileSize: 50,
          unit: 'MB',
          needMd5Validete: true,
          onS3Success: handleS3Success,
          onS3Error: handleS3Error,
        }}
      />
    </div>
  );
};

export default ExcelUploaderExample;
