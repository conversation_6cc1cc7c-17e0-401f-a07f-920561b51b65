.excel-uploader-modal {
  .excel-uploader-content {
    .upload-section {
      padding: 16px 0;

      .upload-item {
        display: flex;
        margin-bottom: 16px;
        &.drag-uploader {
          flex-direction: column;
          align-items: flex-start;
        }
        &.click-uploader {
          align-items: center;
        }
        .label {
          width: 100px;
          text-align: right;
          margin-right: 8px;
        }
        .step-item {
          display: flex;
          align-items: center;
          gap: 12px;
          position: relative;
          font-size: 14px;
          .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #ffffff;
            border: 2px solid #3c6ef0;
            color: #3c6ef0;
            font-weight: 500;
            flex-shrink: 0;
            position: relative;
            z-index: 2;
            &::after {
              content: "";
              position: absolute;
              top: 120%;
              left: 50%;
              transform: translateX(-50%);
              width: 1px;
              height: 20px;
              background-color: #3c6ef0;
              z-index: 1;
            }
            &.last-step::after {
              display: none;
            }
          }
          .step-content {
            flex: 1;
            padding-top: 2px;
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 1.4;
            gap: 8px;
            .step-title {
              font-weight: 500;
              color: #3c6ef0;
            }
            .step-description {
              color: #8c8c8c;
            }
          }
        }
      }

      .upload-description {
        margin-top: 24px;
        padding: 0 24px;

        span {
          color: rgba(0, 0, 0, 0.85);
        }

        p {
          margin: 8px 0 0;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }

  .modal-footer {
    margin-top: 24px;
    text-align: right;

    button {
      margin-left: 8px;
    }
  }

  .ant-progress {
    margin: 16px 24px;
  }
}

.import-error-modal {
  .error-modal-title {
    display: flex;
    align-items: center;
  }

  .error-modal-content {
    padding: 16px 0;

    .import-summary {
      padding: 24px 0;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 24px;

      .summary-item {
        text-align: center;

        .summary-label {
          color: rgba(0, 0, 0, 0.45);
          margin-bottom: 8px;
        }

        .summary-value {
          font-size: 24px;
          font-weight: bold;

          &.total {
            color: #1890ff;
          }

          &.success {
            color: #52c41a;
          }

          &.error {
            color: #ff4d4f;
          }
        }
      }
    }

    .error-tips {
      padding: 16px;
      background: #fff2f0;
      border-radius: 4px;
      margin-bottom: 24px;

      p {
        margin: 0;
        color: rgba(0, 0, 0, 0.85);
      }

      ul {
        margin: 8px 0;
        padding-left: 20px;
        color: rgba(0, 0, 0, 0.65);
      }

      .download-tip {
        color: #ff4d4f;
        margin-top: 8px;
        font-size: 13px;
      }
    }

    .error-modal-footer {
      text-align: right;

      button {
        margin-left: 8px;
      }
    }
  }
}
